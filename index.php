<?php require_once 'includes/config.php'; ?>
<?php require_once 'includes/header.php'; ?>

<!-- Hero Section -->
<div class="hero bg-light py-5 mb-5">
    <div class="container text-center">
        <h1 class="display-4">Find Your Lost Phone</h1>
        <p class="lead">Report lost devices or help reunite phones with their owners</p>
        <div class="d-flex justify-content-center gap-2 mt-4">
            <a href="report.php" class="btn btn-primary btn-lg px-4">Report Lost Phone</a>
            <a href="listing.php" class="btn btn-outline-secondary btn-lg px-4">Browse Listings</a>
        </div>
    </div>
</div>

<!-- How It Works Section -->
<div class="row mb-5">
    <div class="col-md-8 mx-auto">
        <h2 class="text-center mb-4">How It Works</h2>
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="bg-primary text-white rounded-circle p-3 mb-3 mx-auto" style="width: 70px; height: 70px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
                            </svg>
                        </div>
                        <h5>Report Lost</h5>
                        <p>Submit details about your lost phone including location, date, and contact information</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="bg-primary text-white rounded-circle p-3 mb-3 mx-auto" style="width: 70px; height: 70px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                            </svg>
                        </div>
                        <h5>Search Listings</h5>
                        <p>Browse our database of lost and found devices using filters and keywords</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="bg-primary text-white rounded-circle p-3 mb-3 mx-auto" style="width: 70px; height: 70px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                            </svg>
                        </div>
                        <h5>Reunite</h5>
                        <p>Contact the owner or finder through secure channels to recover your device</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Listings -->
<div class="mb-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Recently Lost Phones</h2>
        <a href="listing.php" class="btn btn-sm btn-outline-primary">View All</a>
    </div>
    
    <div class="row">
        <?php
        $stmt = $pdo->query("SELECT * FROM phones WHERE status = 'lost' ORDER BY lost_date DESC LIMIT 3");
        while ($phone = $stmt->fetch()):
        ?>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5 class="card-title"><?= htmlspecialchars($phone['brand']) ?> <?= htmlspecialchars($phone['model']) ?></h5>
                        <span class="badge bg-danger">Lost</span>
                    </div>
                    <p class="card-text">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar me-1" viewBox="0 0 16 16">
                            <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                        </svg>
                        <?= date('M j, Y', strtotime($phone['lost_date'])) ?>
                    </p>
                    <p class="card-text">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-1" viewBox="0 0 16 16">
                            <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                            <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                        </svg>
                        <?= htmlspecialchars($phone['location']) ?>
                    </p>
                </div>
                <div class="card-footer">
                    <a href="detail.php?id=<?= $phone['id'] ?>" class="btn btn-sm btn-primary">View Details</a>
                </div>
            </div>
        </div>
        <?php endwhile; ?>
    </div>
</div>

<!-- Search Form -->
<div class="card shadow mb-5">
    <div class="card-body p-5">
        <h2 class="text-center mb-4">Search Lost Phones</h2>
        <form action="listing.php" method="GET">
            <div class="input-group input-group-lg">
                <input type="text" name="search" class="form-control" placeholder="Search by brand, model, location or IMEI...">
                <button class="btn btn-primary" type="submit">Search</button>
            </div>
        </form>
    </div>
</div>

<!-- Stats Section -->
<div class="row text-center mb-5">
    <div class="col-md-3">
        <h3 class="text-primary">
            <?php 
            $stmt = $pdo->query("SELECT COUNT(*) FROM phones");
            echo number_format($stmt->fetchColumn());
            ?>
        </h3>
        <p>Devices Reported</p>
    </div>
    <div class="col-md-3">
        <h3 class="text-primary">
            <?php 
            $stmt = $pdo->query("SELECT COUNT(*) FROM phones WHERE status = 'found'");
            echo number_format($stmt->fetchColumn());
            ?>
        </h3>
        <p>Phones Recovered</p>
    </div>
    <div class="col-md-3">
        <h3 class="text-primary">
            <?php 
            $stmt = $pdo->query("SELECT COUNT(DISTINCT location) FROM phones");
            echo number_format($stmt->fetchColumn());
            ?>
        </h3>
        <p>Cities Covered</p>
    </div>
    <div class="col-md-3">
        <h3 class="text-primary">
            <?php 
            $stmt = $pdo->query("SELECT COUNT(DISTINCT brand) FROM phones");
            echo number_format($stmt->fetchColumn());
            ?>
        </h3>
        <p>Brands Tracked</p>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>