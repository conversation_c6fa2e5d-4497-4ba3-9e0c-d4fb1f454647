<?php
require_once 'includes/config.php';

// Fetch recent lost phones with error handling
$recent_phones = [];
$stats = [
    'total_devices' => 0,
    'recovered_phones' => 0,
    'cities_covered' => 0,
    'brands_tracked' => 0
];

try {
    // Get recent lost phones
    $stmt = $pdo->prepare("SELECT id, brand, model, lost_date, location FROM phones WHERE status = 'lost' ORDER BY lost_date DESC LIMIT 3");
    $stmt->execute();
    $recent_phones = $stmt->fetchAll();

    // Get statistics
    $stmt = $pdo->query("SELECT COUNT(*) FROM phones");
    $stats['total_devices'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM phones WHERE status = 'found'");
    $stats['recovered_phones'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(DISTINCT location) FROM phones WHERE location IS NOT NULL AND location != ''");
    $stats['cities_covered'] = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(DISTINCT brand) FROM phones WHERE brand IS NOT NULL AND brand != ''");
    $stats['brands_tracked'] = $stmt->fetchColumn();

} catch (PDOException $e) {
    error_log("Database error on index page: " . $e->getMessage());
    // Continue with empty arrays - page will show fallback content
}

require_once 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero bg-light py-5 mb-5" role="banner">
    <div class="container text-center">
        <h1 class="display-4 fw-bold text-primary mb-3">Find Your Lost Phone</h1>
        <p class="lead text-muted mb-4">Report lost devices or help reunite phones with their owners in your community</p>
        <div class="d-flex flex-column flex-sm-row justify-content-center gap-3 mt-4">
            <a href="report.php" class="btn btn-primary btn-lg px-4 shadow-sm" role="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus-circle me-2" viewBox="0 0 16 16" aria-hidden="true">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                </svg>
                Report Lost Phone
            </a>
            <a href="listing.php" class="btn btn-outline-primary btn-lg px-4 shadow-sm" role="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-search me-2" viewBox="0 0 16 16" aria-hidden="true">
                    <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                </svg>
                Browse Listings
            </a>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="container mb-5" aria-labelledby="how-it-works-heading">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h2 id="how-it-works-heading" class="text-center mb-5 fw-bold">How It Works</h2>
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <article class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="card-body p-4">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 70px; height: 70px;" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10zm0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"/>
                                </svg>
                            </div>
                            <h3 class="h5 fw-bold text-primary mb-3">Report Lost</h3>
                            <p class="text-muted">Submit details about your lost phone including location, date, and contact information to help others find it</p>
                        </div>
                    </article>
                </div>

                <div class="col-md-4 mb-4">
                    <article class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="card-body p-4">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 70px; height: 70px;" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                </svg>
                            </div>
                            <h3 class="h5 fw-bold text-primary mb-3">Search Listings</h3>
                            <p class="text-muted">Browse our database of lost and found devices using advanced filters and keyword search</p>
                        </div>
                    </article>
                </div>

                <div class="col-md-4 mb-4">
                    <article class="card h-100 border-0 shadow-sm hover-lift">
                        <div class="card-body p-4">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 70px; height: 70px;" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                                </svg>
                            </div>
                            <h3 class="h5 fw-bold text-primary mb-3">Reunite</h3>
                            <p class="text-muted">Contact the owner or finder through secure channels to safely recover your device</p>
                        </div>
                    </article>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Listings -->
<section class="container mb-5" aria-labelledby="recent-listings-heading">
    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center mb-4 gap-3">
        <h2 id="recent-listings-heading" class="fw-bold mb-0">Recently Lost Phones</h2>
        <a href="listing.php" class="btn btn-outline-primary" role="button">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right me-1" viewBox="0 0 16 16" aria-hidden="true">
                <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
            </svg>
            View All Listings
        </a>
    </div>

    <div class="row">
        <?php if (empty($recent_phones)): ?>
            <div class="col-12">
                <div class="card border-0 bg-light text-center py-5">
                    <div class="card-body">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" class="bi bi-phone text-muted mb-3" viewBox="0 0 16 16" aria-hidden="true">
                            <path d="M1 1a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V1zm1 0v14h12V1H2z"/>
                            <path d="M3 2h10v1H3V2zm0 2h10v1H3V4zm0 2h10v1H3V6z"/>
                        </svg>
                        <h3 class="h5 text-muted">No Recent Reports</h3>
                        <p class="text-muted mb-4">Be the first to report a lost phone in your area</p>
                        <a href="report.php" class="btn btn-primary">Report Lost Phone</a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($recent_phones as $phone): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <article class="card h-100 shadow-sm hover-lift">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h3 class="card-title h5 mb-0 fw-bold">
                                <?= htmlspecialchars($phone['brand'] ?? 'Unknown') ?>
                                <?= htmlspecialchars($phone['model'] ?? 'Model') ?>
                            </h3>
                            <span class="badge bg-danger ms-2">Lost</span>
                        </div>

                        <div class="d-flex align-items-center text-muted mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar me-2" viewBox="0 0 16 16" aria-hidden="true">
                                <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                            </svg>
                            <time datetime="<?= htmlspecialchars($phone['lost_date']) ?>">
                                <?= $phone['lost_date'] ? date('M j, Y', strtotime($phone['lost_date'])) : 'Date unknown' ?>
                            </time>
                        </div>

                        <div class="d-flex align-items-center text-muted">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16" aria-hidden="true">
                                <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                                <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                            </svg>
                            <span><?= htmlspecialchars($phone['location'] ?? 'Location unknown') ?></span>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-top-0 pt-0">
                        <a href="detail.php?id=<?= (int)$phone['id'] ?>" class="btn btn-primary btn-sm w-100" role="button">
                            View Details
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-arrow-right ms-1" viewBox="0 0 16 16" aria-hidden="true">
                                <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                            </svg>
                        </a>
                    </div>
                </article>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Search Form -->
<section class="container mb-5" aria-labelledby="search-heading">
    <div class="card shadow-lg border-0">
        <div class="card-body p-4 p-md-5">
            <h2 id="search-heading" class="text-center mb-4 fw-bold">Search Lost Phones</h2>
            <p class="text-center text-muted mb-4">Find lost devices by searching brand, model, location, or IMEI number</p>

            <form action="listing.php" method="GET" role="search" class="row g-3">
                <div class="col-12">
                    <label for="search-input" class="visually-hidden">Search for lost phones</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text bg-light border-end-0">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-search text-muted" viewBox="0 0 16 16" aria-hidden="true">
                                <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                            </svg>
                        </span>
                        <input
                            type="text"
                            id="search-input"
                            name="search"
                            class="form-control border-start-0 border-end-0"
                            placeholder="Search by brand, model, location or IMEI..."
                            aria-describedby="search-help"
                            autocomplete="off"
                        >
                        <button class="btn btn-primary px-4" type="submit">
                            <span class="d-none d-sm-inline">Search</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right d-sm-none" viewBox="0 0 16 16" aria-hidden="true">
                                <path fill-rule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
                            </svg>
                        </button>
                    </div>
                    <div id="search-help" class="form-text text-center mt-3">
                        <small class="text-muted">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-info-circle me-1" viewBox="0 0 16 16" aria-hidden="true">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                            </svg>
                            Try searching for "iPhone", "Samsung Galaxy", or a specific location
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="container mb-5" aria-labelledby="stats-heading">
    <div class="visually-hidden">
        <h2 id="stats-heading">Platform Statistics</h2>
    </div>

    <div class="row text-center g-4">
        <div class="col-6 col-md-3">
            <div class="card border-0 bg-light h-100 p-3">
                <div class="card-body p-2">
                    <div class="display-6 fw-bold text-primary mb-2" aria-label="<?= number_format($stats['total_devices']) ?> devices reported">
                        <?= number_format($stats['total_devices']) ?>
                    </div>
                    <p class="text-muted mb-0 small fw-medium">Devices Reported</p>
                </div>
            </div>
        </div>

        <div class="col-6 col-md-3">
            <div class="card border-0 bg-light h-100 p-3">
                <div class="card-body p-2">
                    <div class="display-6 fw-bold text-success mb-2" aria-label="<?= number_format($stats['recovered_phones']) ?> phones recovered">
                        <?= number_format($stats['recovered_phones']) ?>
                    </div>
                    <p class="text-muted mb-0 small fw-medium">Phones Recovered</p>
                </div>
            </div>
        </div>

        <div class="col-6 col-md-3">
            <div class="card border-0 bg-light h-100 p-3">
                <div class="card-body p-2">
                    <div class="display-6 fw-bold text-info mb-2" aria-label="<?= number_format($stats['cities_covered']) ?> cities covered">
                        <?= number_format($stats['cities_covered']) ?>
                    </div>
                    <p class="text-muted mb-0 small fw-medium">Cities Covered</p>
                </div>
            </div>
        </div>

        <div class="col-6 col-md-3">
            <div class="card border-0 bg-light h-100 p-3">
                <div class="card-body p-2">
                    <div class="display-6 fw-bold text-warning mb-2" aria-label="<?= number_format($stats['brands_tracked']) ?> brands tracked">
                        <?= number_format($stats['brands_tracked']) ?>
                    </div>
                    <p class="text-muted mb-0 small fw-medium">Brands Tracked</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php require_once 'includes/footer.php'; ?>