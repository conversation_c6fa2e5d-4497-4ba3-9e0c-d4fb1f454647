<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

// Verify CSRF token and get report ID
if (!isset($_GET['id']) || !isset($_GET['token']) || !verifyCSRFToken($_GET['token'])) {
    header('Location: my-reports.php?error=invalid_request');
    exit;
}

$report_id = (int)$_GET['id'];
$user_id = $_SESSION['user_id'];

try {
    // Verify the report belongs to the current user and update status
    $stmt = $pdo->prepare("UPDATE phones SET status = 'found' WHERE id = ? AND user_id = ? AND status = 'lost'");
    $result = $stmt->execute([$report_id, $user_id]);
    
    if ($stmt->rowCount() > 0) {
        header('Location: my-reports.php?success=marked_found');
    } else {
        header('Location: my-reports.php?error=not_found');
    }
} catch (PDOException $e) {
    error_log("Error marking device as found: " . $e->getMessage());
    header('Location: my-reports.php?error=database_error');
}
exit;
