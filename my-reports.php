<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$current_user = getCurrentUser();

// Get user's reports
$reports = [];
try {
    $stmt = $pdo->prepare("SELECT * FROM phones WHERE user_id = ? ORDER BY lost_date DESC");
    $stmt->execute([$current_user['id']]);
    $reports = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching user reports: " . $e->getMessage());
}

$page_title = 'My Reports';
require_once 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fw-bold">My Reports</h1>
    <a href="report.php" class="btn btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle me-2" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
        </svg>
        Report New Device
    </a>
</div>

<?php if (empty($reports)): ?>
    <div class="card border-0 bg-light text-center py-5">
        <div class="card-body">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" class="bi bi-phone text-muted mb-3" viewBox="0 0 16 16">
                <path d="M1 1a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V1zm1 0v14h12V1H2z"/>
                <path d="M3 2h10v1H3V2zm0 2h10v1H3V4zm0 2h10v1H3V6z"/>
            </svg>
            <h3 class="h4 text-muted">No Reports Yet</h3>
            <p class="text-muted mb-4">You haven't reported any lost devices yet.</p>
            <a href="report.php" class="btn btn-primary">Report Your First Device</a>
        </div>
    </div>
<?php else: ?>
    <div class="row">
        <?php foreach ($reports as $report): ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card h-100 shadow-sm hover-lift">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0 fw-bold">
                                <?= htmlspecialchars($report['brand']) ?> <?= htmlspecialchars($report['model']) ?>
                            </h5>
                            <span class="badge bg-<?= $report['status'] == 'lost' ? 'danger' : 'success' ?> ms-2">
                                <?= ucfirst($report['status']) ?>
                            </span>
                        </div>
                        
                        <div class="mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-calendar me-2 text-muted" viewBox="0 0 16 16">
                                <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                            </svg>
                            <small class="text-muted">
                                Lost: <?= date('M j, Y', strtotime($report['lost_date'])) ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-geo-alt me-2 text-muted" viewBox="0 0 16 16">
                                <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                                <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                            </svg>
                            <small class="text-muted"><?= htmlspecialchars($report['location']) ?></small>
                        </div>
                        
                        <?php if (!empty($report['imei'])): ?>
                            <div class="mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-hash me-2 text-muted" viewBox="0 0 16 16">
                                    <path d="M8.39 12.648a1.32 1.32 0 0 0-.015.18c0 .305.21.508.5.508.266 0 .492-.172.555-.477l.554-2.703h1.204c.421 0 .617-.234.617-.547 0-.312-.188-.53-.617-.53h-.985l.516-2.524h1.204c.421 0 .617-.234.617-.547 0-.312-.188-.53-.617-.53h-.985l.516-2.524c.062-.305-.172-.477-.555-.477-.266 0-.492.172-.555.477l-.554 2.703H7.617l.516-2.524c.062-.305-.172-.477-.555-.477-.266 0-.492.172-.555.477l-.554 2.703H5.265c-.421 0-.617.234-.617.547 0 .312.188.53.617.53h.985l-.516 2.524H4.53c-.421 0-.617.234-.617.547 0 .312.188.53.617.53h.985l-.516 2.524z"/>
                                </svg>
                                <small class="text-muted">IMEI: <?= htmlspecialchars($report['imei']) ?></small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($report['description'])): ?>
                            <p class="card-text text-muted small mt-3">
                                <?= htmlspecialchars(substr($report['description'], 0, 100)) ?>
                                <?= strlen($report['description']) > 100 ? '...' : '' ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-footer bg-transparent border-top-0 pt-0">
                        <div class="d-flex gap-2">
                            <a href="detail.php?id=<?= (int)$report['id'] ?>" class="btn btn-sm btn-outline-primary flex-fill">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-eye me-1" viewBox="0 0 16 16">
                                    <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"/>
                                    <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                                </svg>
                                View
                            </a>
                            <?php if ($report['status'] == 'lost'): ?>
                                <button class="btn btn-sm btn-outline-success flex-fill" onclick="markAsFound(<?= $report['id'] ?>)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-check-circle me-1" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.235.235 0 0 0-.01-.05.75.75 0 0 0-1.071-.01z"/>
                                    </svg>
                                    Mark Found
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<script>
function markAsFound(reportId) {
    if (confirm('Are you sure you want to mark this device as found?')) {
        // In a real application, you would make an AJAX call here
        // For now, we'll redirect to a simple PHP script
        window.location.href = 'mark-found.php?id=' + reportId + '&token=<?= generateCSRFToken() ?>';
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
