<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$current_user = getCurrentUser();
$page_title = 'My Profile';
require_once 'includes/header.php';
?>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow-lg border-0">
            <div class="card-header bg-primary text-white">
                <h2 class="mb-0">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-person-circle me-2" viewBox="0 0 16 16">
                        <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>
                        <path fill-rule="evenodd" d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z"/>
                    </svg>
                    My Profile
                </h2>
            </div>
            <div class="card-body p-4">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-muted mb-3">Account Information</h5>
                        
                        <div class="mb-3">
                            <label class="form-label fw-medium text-muted">Full Name</label>
                            <p class="fs-5"><?= htmlspecialchars($current_user['full_name']) ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-medium text-muted">Username</label>
                            <p class="fs-5"><?= htmlspecialchars($current_user['username']) ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-medium text-muted">Email Address</label>
                            <p class="fs-5"><?= htmlspecialchars($current_user['email']) ?></p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="text-muted mb-3">Account Statistics</h5>
                        
                        <?php
                        // Get user statistics
                        try {
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM phones WHERE user_id = ?");
                            $stmt->execute([$current_user['id']]);
                            $total_reports = $stmt->fetchColumn();
                            
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM phones WHERE user_id = ? AND status = 'found'");
                            $stmt->execute([$current_user['id']]);
                            $found_reports = $stmt->fetchColumn();
                            
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM phones WHERE user_id = ? AND status = 'lost'");
                            $stmt->execute([$current_user['id']]);
                            $lost_reports = $stmt->fetchColumn();
                        } catch (PDOException $e) {
                            $total_reports = $found_reports = $lost_reports = 0;
                        }
                        ?>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="card bg-light border-0">
                                    <div class="card-body p-3">
                                        <h3 class="text-primary mb-1"><?= $total_reports ?></h3>
                                        <small class="text-muted">Total Reports</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card bg-light border-0">
                                    <div class="card-body p-3">
                                        <h3 class="text-danger mb-1"><?= $lost_reports ?></h3>
                                        <small class="text-muted">Lost</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card bg-light border-0">
                                    <div class="card-body p-3">
                                        <h3 class="text-success mb-1"><?= $found_reports ?></h3>
                                        <small class="text-muted">Found</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label class="form-label fw-medium text-muted">Member Since</label>
                            <p class="fs-6"><?= date('F j, Y', strtotime($current_user['created_at'])) ?></p>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="d-flex gap-2 flex-wrap">
                    <a href="my-reports.php" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-list-check me-2" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM3.854 2.146a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708L2 3.293l1.146-1.147a.5.5 0 0 1 .708 0zm0 4a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 1 1 .708-.708L2 7.293l1.146-1.147a.5.5 0 0 1 .708 0zm0 4a.5.5 0 0 1 0 .708l-1.5 1.5a.5.5 0 0 1-.708 0l-.5-.5a.5.5 0 0 1 .708-.708L2 11.293l1.146-1.147a.5.5 0 0 1 .708 0z"/>
                        </svg>
                        View My Reports
                    </a>
                    <a href="report.php" class="btn btn-outline-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle me-2" viewBox="0 0 16 16">
                            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                        </svg>
                        Report New Device
                    </a>
                    <a href="listing.php" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search me-2" viewBox="0 0 16 16">
                            <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                        </svg>
                        Browse All Listings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
