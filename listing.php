<?php require_once 'includes/config.php'; ?>
<?php require_once 'includes/header.php'; ?>

<h1 class="mb-4">Lost Phones</h1>

<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="Search by brand, model or location">
                <button class="btn btn-outline-secondary" type="submit">Search</button>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <?php
    $search = $_GET['search'] ?? '';
    $sql = "SELECT * FROM phones WHERE status = 'lost'";
    
    if (!empty($search)) {
        $sql .= " AND (brand LIKE :search OR model LIKE :search OR location LIKE :search)";
    }
    
    $sql .= " ORDER BY lost_date DESC";
    $stmt = $pdo->prepare($sql);
    
    if (!empty($search)) {
        $stmt->execute([':search' => "%$search%"]);
    } else {
        $stmt->execute();
    }
    
    while ($phone = $stmt->fetch()):
    ?>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title"><?= htmlspecialchars($phone['brand']) ?> <?= htmlspecialchars($phone['model']) ?></h5>
                <p class="card-text">
                    <strong>Lost:</strong> <?= date('M j, Y', strtotime($phone['lost_date'])) ?><br>
                    <strong>Location:</strong> <?= htmlspecialchars($phone['location']) ?><br>
                    <?php if (!empty($phone['imei'])): ?>
                    <strong>IMEI:</strong> <?= htmlspecialchars($phone['imei']) ?>
                    <?php endif; ?>
                </p>
            </div>
            <div class="card-footer">
                <a href="detail.php?id=<?= $phone['id'] ?>" class="btn btn-sm btn-primary">View Details</a>
            </div>
        </div>
    </div>
    <?php endwhile; ?>
</div>

<?php require_once 'includes/footer.php'; ?>