<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Require user to be logged in
requireLogin();

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Get and sanitize form data
        $brand = sanitizeInput($_POST['brand'] ?? '');
        $model = sanitizeInput($_POST['model'] ?? '');
        $imei = sanitizeInput($_POST['imei'] ?? '');
        $lost_date = $_POST['lost_date'] ?? '';
        $location = sanitizeInput($_POST['location'] ?? '');
        $contact = sanitizeInput($_POST['contact'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');

        // Validation
        if (empty($brand)) {
            $errors[] = 'Brand is required.';
        }

        if (empty($model)) {
            $errors[] = 'Model is required.';
        }

        if (empty($lost_date)) {
            $errors[] = 'Lost date is required.';
        } elseif (strtotime($lost_date) > time()) {
            $errors[] = 'Lost date cannot be in the future.';
        }

        if (empty($location)) {
            $errors[] = 'Location is required.';
        }

        if (empty($contact)) {
            $errors[] = 'Contact information is required.';
        }

        if (!empty($imei) && !preg_match('/^\d{15}$/', $imei)) {
            $errors[] = 'IMEI must be exactly 15 digits.';
        }

        // If no errors, insert the report
        if (empty($errors)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO phones
                    (brand, model, imei, lost_date, location, contact, description, user_id, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'lost')");

                if ($stmt->execute([$brand, $model, $imei ?: null, $lost_date, $location, $contact, $description, $_SESSION['user_id']])) {
                    $success = true;
                } else {
                    $errors[] = 'Error submitting report. Please try again.';
                }
            } catch (PDOException $e) {
                error_log("Error submitting report: " . $e->getMessage());
                $errors[] = 'Database error. Please try again.';
            }
        }
    }
}

$page_title = 'Report Lost Phone';
require_once 'includes/header.php';
?>

<?php if ($success): ?>
<div class="alert alert-success" role="alert">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle me-2" viewBox="0 0 16 16">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.235.235 0 0 0-.01-.05.75.75 0 0 0-1.071-.01z"/>
    </svg>
    <strong>Report submitted successfully!</strong> Your lost phone report has been added to our database.
    <div class="mt-2">
        <a href="listing.php" class="btn btn-sm btn-outline-success me-2">View All Listings</a>
        <a href="my-reports.php" class="btn btn-sm btn-outline-primary">View My Reports</a>
    </div>
</div>
<?php endif; ?>

<?php if (!empty($errors)): ?>
<div class="alert alert-danger" role="alert">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-exclamation-triangle me-2" viewBox="0 0 16 16">
        <path d="M7.938 2.016A.13.13 0 0 1 8.002 2a.13.13 0 0 1 .063.016.146.146 0 0 1 .054.057l6.857 11.667c.036.06.035.124.002.183a.163.163 0 0 1-.054.06.116.116 0 0 1-.066.017H1.146a.115.115 0 0 1-.066-.017.163.163 0 0 1-.054-.06.176.176 0 0 1 .002-.183L7.884 2.073a.147.147 0 0 1 .054-.057zm1.044-.45a1.13 1.13 0 0 0-2.008 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566z"/>
        <path d="M7.002 12a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 5.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995z"/>
    </svg>
    <strong>Please fix the following errors:</strong>
    <ul class="mb-0 mt-2">
        <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
        <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>

<div class="card shadow-lg border-0">
    <div class="card-header bg-primary text-white">
        <h2 class="mb-0">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="bi bi-plus-circle me-2" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
            </svg>
            Report Lost Phone
        </h2>
        <p class="mb-0 mt-2 opacity-75">Help others find your lost device by providing detailed information</p>
    </div>
    <div class="card-body p-4">
        <form method="POST" novalidate>
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="brand" class="form-label fw-medium">Brand <span class="text-danger">*</span></label>
                    <input type="text"
                           id="brand"
                           name="brand"
                           class="form-control"
                           value="<?= htmlspecialchars($_POST['brand'] ?? '') ?>"
                           required
                           placeholder="e.g., Apple, Samsung, Google">
                </div>
                <div class="col-md-6">
                    <label for="model" class="form-label fw-medium">Model <span class="text-danger">*</span></label>
                    <input type="text"
                           id="model"
                           name="model"
                           class="form-control"
                           value="<?= htmlspecialchars($_POST['model'] ?? '') ?>"
                           required
                           placeholder="e.g., iPhone 14, Galaxy S23">
                </div>
            </div>

            <div class="mb-3">
                <label for="imei" class="form-label fw-medium">IMEI Number (Optional)</label>
                <input type="text"
                       id="imei"
                       name="imei"
                       class="form-control"
                       value="<?= htmlspecialchars($_POST['imei'] ?? '') ?>"
                       pattern="\d{15}"
                       maxlength="15"
                       placeholder="15-digit number">
                <div class="form-text">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" class="bi bi-info-circle me-1" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                        <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                    </svg>
                    15-digit number usually found in Settings > About Phone or under the battery
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="lost_date" class="form-label fw-medium">Date Lost <span class="text-danger">*</span></label>
                    <input type="date"
                           id="lost_date"
                           name="lost_date"
                           class="form-control"
                           value="<?= htmlspecialchars($_POST['lost_date'] ?? '') ?>"
                           max="<?= date('Y-m-d') ?>"
                           required>
                </div>
                <div class="col-md-6">
                    <label for="location" class="form-label fw-medium">Location <span class="text-danger">*</span></label>
                    <input type="text"
                           id="location"
                           name="location"
                           class="form-control"
                           value="<?= htmlspecialchars($_POST['location'] ?? '') ?>"
                           required
                           placeholder="e.g., Central Park, NYC or Mall of America">
                </div>
            </div>

            <div class="mb-3">
                <label for="contact" class="form-label fw-medium">Contact Information <span class="text-danger">*</span></label>
                <input type="text"
                       id="contact"
                       name="contact"
                       class="form-control"
                       value="<?= htmlspecialchars($_POST['contact'] ?? '') ?>"
                       required
                       placeholder="Email or phone number">
                <div class="form-text">Email or phone number where you can be reached if someone finds your device</div>
            </div>

            <div class="mb-4">
                <label for="description" class="form-label fw-medium">Additional Description</label>
                <textarea id="description"
                          name="description"
                          class="form-control"
                          rows="4"
                          placeholder="Describe any distinctive features, case color, screen protector, etc."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                <div class="form-text">Include details like color, case type, distinctive marks, or any other identifying features</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="index.php" class="btn btn-outline-secondary me-md-2">Cancel</a>
                <button type="submit" class="btn btn-primary btn-lg px-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-send me-2" viewBox="0 0 16 16">
                        <path d="M15.854.146a.5.5 0 0 1 .11.54L13.026 8.5l2.938 7.814a.5.5 0 0 1-.********* 0 0 1-.54.11L8.5 13.026 .686 15.964a.5.5 0 0 1-.54-.11.5.5 0 0 1-.11-.54L2.974 7.5.036-.314a.5.5 0 0 1 .11-.54.5.5 0 0 1 .54-.11L8.5 2.974 15.314.036a.5.5 0 0 1 .54.11z"/>
                    </svg>
                    Submit Report
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>