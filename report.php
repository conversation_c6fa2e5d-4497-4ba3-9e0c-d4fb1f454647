<?php require_once 'includes/config.php'; ?>
<?php require_once 'includes/header.php'; ?>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $brand = $_POST['brand'];
    $model = $_POST['model'];
    $imei = $_POST['imei'];
    $lost_date = $_POST['lost_date'];
    $location = $_POST['location'];
    $contact = $_POST['contact'];
    $description = $_POST['description'];
    
    $stmt = $pdo->prepare("INSERT INTO phones 
        (brand, model, imei, lost_date, location, contact, description) 
        VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    if ($stmt->execute([$brand, $model, $imei, $lost_date, $location, $contact, $description])) {
        echo '<div class="alert alert-success">Report submitted successfully!</div>';
    } else {
        echo '<div class="alert alert-danger">Error submitting report</div>';
    }
}
?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h2>Report Lost Phone</h2>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label>Brand</label>
                    <input type="text" name="brand" class="form-control" required>
                </div>
                <div class="col-md-6">
                    <label>Model</label>
                    <input type="text" name="model" class="form-control" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label>IMEI Number (Optional)</label>
                <input type="text" name="imei" class="form-control">
                <small class="text-muted">15-digit number usually found under battery</small>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label>Date Lost</label>
                    <input type="date" name="lost_date" class="form-control" required>
                </div>
                <div class="col-md-6">
                    <label>Location</label>
                    <input type="text" name="location" class="form-control" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label>Contact Information</label>
                <input type="text" name="contact" class="form-control" required>
                <small class="text-muted">Email or phone number where you can be reached</small>
            </div>
            
            <div class="mb-3">
                <label>Additional Description</label>
                <textarea name="description" class="form-control" rows="4"></textarea>
                <small class="text-muted">Color, case, distinctive marks, etc.</small>
            </div>
            
            <button type="submit" class="btn btn-primary">Submit Report</button>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>