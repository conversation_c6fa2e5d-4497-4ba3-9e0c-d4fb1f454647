<?php
return [
    // Common
    'app_name' => 'PhoneFinder',
    'app_tagline' => 'Find Your Lost Phone',
    'app_description' => 'Report lost devices or help reunite phones with their owners in your community',
    'loading' => 'Loading...',
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Information',
    'close' => 'Close',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'search' => 'Search',
    'filter' => 'Filter',
    'reset' => 'Reset',
    'submit' => 'Submit',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'home' => 'Home',
    'yes' => 'Yes',
    'no' => 'No',
    'required' => 'Required',
    'optional' => 'Optional',
    'welcome' => 'Welcome',
    
    // Navigation
    'nav_home' => 'Home',
    'nav_report_lost' => 'Report Lost',
    'nav_browse_listings' => 'Browse Listings',
    'nav_login' => 'Login',
    'nav_register' => 'Register',
    'nav_profile' => 'My Profile',
    'nav_my_reports' => 'My Reports',
    'nav_logout' => 'Logout',
    
    // Authentication
    'login_title' => 'Welcome Back',
    'login_subtitle' => 'Sign in to your PhoneFinder account',
    'register_title' => 'Create Account',
    'register_subtitle' => 'Join PhoneFinder to report and track lost devices',
    'username_or_email' => 'Username or Email',
    'username' => 'Username',
    'email' => 'Email Address',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'full_name' => 'Full Name',
    'remember_me' => 'Remember me for 30 days',
    'sign_in' => 'Sign In',
    'create_account' => 'Create Account',
    'already_have_account' => 'Already have an account?',
    'dont_have_account' => "Don't have an account?",
    'sign_in_here' => 'Sign in here',
    'create_one_here' => 'Create one here',
    'logout_success' => 'You have been successfully logged out. Thank you for using PhoneFinder!',
    
    // Validation Messages
    'username_required' => 'Username is required.',
    'username_min_length' => 'Username must be at least 3 characters long.',
    'username_invalid_chars' => 'Username can only contain letters, numbers, and underscores.',
    'username_exists' => 'Username already exists. Please choose another.',
    'email_required' => 'Email is required.',
    'email_invalid' => 'Please enter a valid email address.',
    'email_exists' => 'Email already registered. Please use another email or login.',
    'full_name_required' => 'Full name is required.',
    'full_name_min_length' => 'Full name must be at least 2 characters long.',
    'password_required' => 'Password is required.',
    'password_invalid' => 'Password must be at least 8 characters long and contain both letters and numbers.',
    'passwords_not_match' => 'Passwords do not match.',
    'invalid_credentials' => 'Invalid username/email or password.',
    'invalid_token' => 'Invalid security token. Please try again.',
    
    // Home Page
    'hero_title' => 'Find Your Lost Phone',
    'hero_description' => 'Report lost devices or help reunite phones with their owners in your community',
    'report_lost_phone' => 'Report Lost Phone',
    'browse_listings' => 'Browse Listings',
    'how_it_works' => 'How It Works',
    'report_lost' => 'Report Lost',
    'report_lost_desc' => 'Submit details about your lost phone including location, date, and contact information to help others find it',
    'search_listings' => 'Search Listings',
    'search_listings_desc' => 'Browse our database of lost and found devices using advanced filters and keyword search',
    'reunite' => 'Reunite',
    'reunite_desc' => 'Contact the owner or finder through secure channels to safely recover your device',
    'recently_lost_phones' => 'Recently Lost Phones',
    'view_all_listings' => 'View All Listings',
    'no_recent_reports' => 'No Recent Reports',
    'no_recent_reports_desc' => 'Be the first to report a lost phone in your area',
    'search_lost_phones' => 'Search Lost Phones',
    'search_description' => 'Find lost devices by searching brand, model, location, or IMEI number',
    'search_placeholder' => 'Search by brand, model, location or IMEI...',
    'search_help' => 'Try searching for "iPhone", "Samsung Galaxy", or a specific location',
    
    // Statistics
    'devices_reported' => 'Devices Reported',
    'phones_recovered' => 'Phones Recovered',
    'cities_covered' => 'Cities Covered',
    'brands_tracked' => 'Brands Tracked',
    
    // Report Form
    'report_lost_phone_title' => 'Report Lost Phone',
    'report_form_description' => 'Help others find your lost device by providing detailed information',
    'brand' => 'Brand',
    'model' => 'Model',
    'imei_number' => 'IMEI Number',
    'date_lost' => 'Date Lost',
    'location' => 'Location',
    'contact_information' => 'Contact Information',
    'additional_description' => 'Additional Description',
    'brand_placeholder' => 'e.g., Apple, Samsung, Google',
    'model_placeholder' => 'e.g., iPhone 14, Galaxy S23',
    'imei_placeholder' => '15-digit number',
    'imei_help' => '15-digit number usually found in Settings > About Phone or under the battery',
    'location_placeholder' => 'e.g., Central Park, NYC or Mall of America',
    'contact_placeholder' => 'Email or phone number',
    'contact_help' => 'Email or phone number where you can be reached if someone finds your device',
    'description_placeholder' => 'Describe any distinctive features, case color, screen protector, etc.',
    'description_help' => 'Include details like color, case type, distinctive marks, or any other identifying features',
    'submit_report' => 'Submit Report',
    'report_success' => 'Report submitted successfully! Your lost phone report has been added to our database.',
    'view_my_reports' => 'View My Reports',
    'report_new_device' => 'Report New Device',
    
    // Device Status
    'lost' => 'Lost',
    'found' => 'Found',
    'status' => 'Status',
    'mark_found' => 'Mark Found',
    'mark_as_found' => 'Mark as Found',
    'confirm_mark_found' => 'Are you sure you want to mark this device as found?',
    
    // Profile
    'my_profile' => 'My Profile',
    'account_information' => 'Account Information',
    'account_statistics' => 'Account Statistics',
    'total_reports' => 'Total Reports',
    'member_since' => 'Member Since',
    'browse_all_listings' => 'Browse All Listings',
    
    // My Reports
    'my_reports' => 'My Reports',
    'no_reports_yet' => 'No Reports Yet',
    'no_reports_desc' => "You haven't reported any lost devices yet.",
    'report_first_device' => 'Report Your First Device',
    'view_details' => 'View Details',
    
    // Device Details
    'device_details' => 'Device Details',
    'date_unknown' => 'Date unknown',
    'location_unknown' => 'Location unknown',
    
    // Footer
    'footer_description' => 'Helping reunite lost phones with their owners through community support.',
    'quick_links' => 'Quick Links',
    'all_rights_reserved' => 'All rights reserved.',
    'made_with_love' => 'Made with ❤️ for the community',
    
    // Error Messages
    'error_occurred' => 'An error occurred',
    'database_error' => 'Database error. Please try again.',
    'not_found' => 'Not found',
    'access_denied' => 'Access denied',
    'invalid_request' => 'Invalid request',
    'please_login' => 'Please login to continue',
    'session_expired' => 'Your session has expired. Please login again.',
    
    // Form Validation
    'field_required' => 'This field is required.',
    'invalid_email_format' => 'Please enter a valid email address.',
    'password_too_short' => 'Password must be at least 8 characters long.',
    'invalid_date' => 'Please enter a valid date.',
    'date_future' => 'Date cannot be in the future.',
    'imei_invalid' => 'IMEI must be exactly 15 digits.',
    
    // Language Switcher
    'language' => 'Language',
    'switch_language' => 'Switch Language',
    'english' => 'English',
    'arabic' => 'العربية',
];
