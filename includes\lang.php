<?php
// Language management system

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Available languages
$available_languages = [
    'en' => [
        'name' => 'English',
        'native_name' => 'English',
        'dir' => 'ltr',
        'flag' => '🇺🇸'
    ],
    'ar' => [
        'name' => 'Arabic',
        'native_name' => 'العربية',
        'dir' => 'rtl',
        'flag' => '🇸🇦'
    ]
];

/**
 * Get current language
 */
function getCurrentLanguage() {
    global $available_languages;
    
    // Check URL parameter first
    if (isset($_GET['lang']) && array_key_exists($_GET['lang'], $available_languages)) {
        $_SESSION['language'] = $_GET['lang'];
        return $_GET['lang'];
    }
    
    // Check session
    if (isset($_SESSION['language']) && array_key_exists($_SESSION['language'], $available_languages)) {
        return $_SESSION['language'];
    }
    
    // Check browser language
    if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        $browser_lang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
        if (array_key_exists($browser_lang, $available_languages)) {
            $_SESSION['language'] = $browser_lang;
            return $browser_lang;
        }
    }
    
    // Default to English
    $_SESSION['language'] = 'en';
    return 'en';
}

/**
 * Get language direction
 */
function getLanguageDirection() {
    global $available_languages;
    $current_lang = getCurrentLanguage();
    return $available_languages[$current_lang]['dir'];
}

/**
 * Check if current language is RTL
 */
function isRTL() {
    return getLanguageDirection() === 'rtl';
}

/**
 * Load language translations
 */
function loadTranslations($lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    $translations_file = __DIR__ . "/translations/{$lang}.php";
    
    if (file_exists($translations_file)) {
        return include $translations_file;
    }
    
    // Fallback to English
    $fallback_file = __DIR__ . "/translations/en.php";
    if (file_exists($fallback_file)) {
        return include $fallback_file;
    }
    
    return [];
}

/**
 * Get translation for a key
 */
function t($key, $params = []) {
    static $translations = null;
    
    if ($translations === null) {
        $translations = loadTranslations();
    }
    
    // Get translation or return key if not found
    $translation = $translations[$key] ?? $key;
    
    // Replace parameters
    if (!empty($params)) {
        foreach ($params as $param_key => $param_value) {
            $translation = str_replace("{{$param_key}}", $param_value, $translation);
        }
    }
    
    return $translation;
}

/**
 * Get language switcher URL
 */
function getLanguageSwitcherUrl($target_lang) {
    $current_url = $_SERVER['REQUEST_URI'];
    $parsed_url = parse_url($current_url);
    
    // Parse existing query parameters
    $query_params = [];
    if (isset($parsed_url['query'])) {
        parse_str($parsed_url['query'], $query_params);
    }
    
    // Set or update language parameter
    $query_params['lang'] = $target_lang;
    
    // Build new URL
    $new_query = http_build_query($query_params);
    $new_url = $parsed_url['path'] . ($new_query ? '?' . $new_query : '');
    
    return $new_url;
}

/**
 * Format date according to language
 */
function formatDate($date, $format = null) {
    $current_lang = getCurrentLanguage();
    
    if ($current_lang === 'ar') {
        // Arabic date formatting
        if ($format === null) {
            $format = 'j F Y'; // Default Arabic format
        }
        
        $arabic_months = [
            'January' => 'يناير',
            'February' => 'فبراير', 
            'March' => 'مارس',
            'April' => 'أبريل',
            'May' => 'مايو',
            'June' => 'يونيو',
            'July' => 'يوليو',
            'August' => 'أغسطس',
            'September' => 'سبتمبر',
            'October' => 'أكتوبر',
            'November' => 'نوفمبر',
            'December' => 'ديسمبر'
        ];
        
        $formatted_date = date($format, strtotime($date));
        
        // Replace English month names with Arabic
        foreach ($arabic_months as $english => $arabic) {
            $formatted_date = str_replace($english, $arabic, $formatted_date);
        }
        
        return $formatted_date;
    }
    
    // Default English formatting
    if ($format === null) {
        $format = 'F j, Y';
    }
    
    return date($format, strtotime($date));
}

/**
 * Format numbers according to language
 */
function formatNumber($number) {
    $current_lang = getCurrentLanguage();
    
    if ($current_lang === 'ar') {
        // Convert to Arabic-Indic numerals
        $arabic_numerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english_numerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($english_numerals, $arabic_numerals, number_format($number));
    }
    
    return number_format($number);
}

// Initialize current language
$current_language = getCurrentLanguage();
$language_direction = getLanguageDirection();
$is_rtl = isRTL();
