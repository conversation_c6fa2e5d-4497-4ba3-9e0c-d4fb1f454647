/* Hero Section */
.hero {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(13,110,253,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Card Animations */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
}

/* Search Form Enhancements */
.input-group-text {
    border: 2px solid #dee2e6;
}

.form-control {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

/* Button Enhancements */
.btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Stats Cards */
.display-6 {
    font-size: 2.5rem;
    line-height: 1.2;
}

/* Responsive Improvements */
@media (max-width: 576px) {
    .hero .display-4 {
        font-size: 2rem;
    }

    .display-6 {
        font-size: 1.8rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Focus Improvements for Accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Custom Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* Icon Alignment */
.bi {
    vertical-align: -0.125em;
}