/* ===== FONT FAMILIES ===== */
/* Cairo font for Arabic */
body[data-lang="ar"],
.rtl {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Default font for English */
body[data-lang="en"],
.ltr {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* ===== RTL/LTR LAYOUT SUPPORT ===== */
.rtl {
    direction: rtl !important;
    text-align: right !important;
}

.ltr {
    direction: ltr !important;
    text-align: left !important;
}

/* RTL Navigation Adjustments */
.rtl .navbar-nav {
    flex-direction: row-reverse;
}

.rtl .navbar-brand {
    margin-left: auto;
    margin-right: 0;
}

.rtl .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    text-align: right;
}

.rtl .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

/* RTL Text Alignment */
.rtl .text-start {
    text-align: right !important;
}

.rtl .text-end {
    text-align: left !important;
}

.rtl .text-center {
    text-align: center !important;
}

/* RTL Margin/Padding Adjustments */
.rtl .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
.rtl .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
.rtl .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
.rtl .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
.rtl .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
.rtl .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }

/* RTL Flex Direction */
.rtl .d-flex {
    flex-direction: row-reverse;
}

.rtl .flex-row {
    flex-direction: row-reverse !important;
}

.rtl .justify-content-start {
    justify-content: flex-end !important;
}

.rtl .justify-content-end {
    justify-content: flex-start !important;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(13,110,253,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: 1;
    pointer-events: none;
}

.hero .container {
    position: relative;
    z-index: 2;
}

/* Card Animations */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15) !important;
}

/* Search Form Enhancements */
.input-group-text {
    border: 2px solid #dee2e6;
}

.form-control {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

/* Button Enhancements */
.btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Stats Cards */
.display-6 {
    font-size: 2.5rem;
    line-height: 1.2;
}

/* Responsive Improvements */
@media (max-width: 576px) {
    .hero .display-4 {
        font-size: 2rem;
    }

    .display-6 {
        font-size: 1.8rem;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Focus Improvements for Accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Custom Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* Icon Alignment */
.bi {
    vertical-align: -0.125em;
}

/* ===== ADDITIONAL RTL SUPPORT ===== */
/* RTL Form Controls */
.rtl .form-control,
.rtl .form-select,
.rtl .form-check-input {
    text-align: right;
}

.rtl .input-group .form-control:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.rtl .input-group .form-control:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* RTL Button Groups */
.rtl .btn-group > .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.rtl .btn-group > .btn:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* RTL Card Layouts */
.rtl .card-header,
.rtl .card-body,
.rtl .card-footer {
    text-align: right;
}

/* RTL List Groups */
.rtl .list-group-item {
    text-align: right;
}

/* RTL Breadcrumbs */
.rtl .breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-right: 0;
    padding-left: 0.5rem;
    content: "‹";
}

/* RTL Pagination */
.rtl .page-link {
    text-align: center;
}

/* ===== LANGUAGE-SPECIFIC TYPOGRAPHY ===== */
/* Arabic Typography Improvements */
.rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
    font-weight: 600;
    line-height: 1.4;
}

.rtl p, .rtl .lead {
    line-height: 1.8;
}

.rtl .display-1, .rtl .display-2, .rtl .display-3,
.rtl .display-4, .rtl .display-5, .rtl .display-6 {
    font-weight: 700;
    line-height: 1.2;
}

/* Arabic Number Styling */
.rtl .badge,
.rtl .btn,
.rtl .alert {
    font-feature-settings: "tnum";
}

/* ===== RESPONSIVE RTL ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .rtl .navbar-nav {
        text-align: right;
    }

    .rtl .navbar-collapse {
        text-align: right;
    }

    .rtl .offcanvas-header {
        text-align: right;
    }
}

/* ===== ANIMATION IMPROVEMENTS ===== */
/* Smooth transitions for language switching */
html {
    transition: direction 0.3s ease;
}

body {
    transition: font-family 0.3s ease;
}

/* RTL-aware hover effects */
.rtl .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
}

.rtl .btn:hover {
    transform: translateY(-2px) scale(1.05);
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
/* High contrast focus indicators */
.rtl .btn:focus,
.rtl .form-control:focus,
.rtl .form-select:focus {
    outline: 3px solid #0d6efd;
    outline-offset: 2px;
}

/* Screen reader improvements */
.rtl .visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* ===== PRINT STYLES ===== */
@media print {
    .rtl {
        direction: rtl;
        text-align: right;
    }

    .rtl .navbar,
    .rtl .btn,
    .rtl .dropdown {
        display: none !important;
    }
}