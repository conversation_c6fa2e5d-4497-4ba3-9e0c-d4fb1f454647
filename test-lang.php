<?php 
require_once 'includes/config.php'; 
require_once 'includes/auth.php';
require_once 'includes/lang.php';

$page_title = 'Language Test';
require_once 'includes/header.php'; 
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><?= t('app_name') ?> - Language Test</h1>
            
            <div class="alert alert-info">
                <h5>Current Language Settings:</h5>
                <ul>
                    <li><strong>Language:</strong> <?= $current_language ?></li>
                    <li><strong>Direction:</strong> <?= $language_direction ?></li>
                    <li><strong>Is RTL:</strong> <?= $is_rtl ? 'Yes' : 'No' ?></li>
                    <li><strong>Body Class:</strong> <?= $is_rtl ? 'rtl' : 'ltr' ?></li>
                </ul>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><?= t('app_name') ?></h5>
                        </div>
                        <div class="card-body">
                            <h6><?= t('hero_title') ?></h6>
                            <p><?= t('hero_description') ?></p>
                            
                            <div class="d-flex gap-2 mb-3">
                                <a href="report.php" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle <?= $is_rtl ? 'ms-1' : 'me-1' ?>" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    <?= t('report_lost_phone') ?>
                                </a>
                                <a href="listing.php" class="btn btn-outline-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-search <?= $is_rtl ? 'ms-1' : 'me-1' ?>" viewBox="0 0 16 16">
                                        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                    </svg>
                                    <?= t('browse_listings') ?>
                                </a>
                            </div>
                            
                            <h6><?= t('how_it_works') ?></h6>
                            <ol>
                                <li><?= t('report_lost') ?> - <?= t('report_lost_desc') ?></li>
                                <li><?= t('search_listings') ?> - <?= t('search_listings_desc') ?></li>
                                <li><?= t('reunite') ?> - <?= t('reunite_desc') ?></li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Statistics Test</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3 class="text-primary"><?= formatNumber(1234) ?></h3>
                                            <small><?= t('devices_reported') ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h3 class="text-success"><?= formatNumber(567) ?></h3>
                                            <small><?= t('phones_recovered') ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h6>Date Formatting Test:</h6>
                            <p><?= formatDate('2024-01-15') ?></p>
                            
                            <h6>Form Test:</h6>
                            <form>
                                <div class="mb-3">
                                    <label class="form-label"><?= t('brand') ?></label>
                                    <input type="text" class="form-control" placeholder="<?= t('brand_placeholder') ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><?= t('model') ?></label>
                                    <input type="text" class="form-control" placeholder="<?= t('model_placeholder') ?>">
                                </div>
                                <button type="button" class="btn btn-primary"><?= t('submit') ?></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Language Switcher Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Click the language switcher in the navigation to test RTL/LTR switching.</p>
                        <div class="d-flex gap-2">
                            <a href="?lang=en" class="btn btn-outline-primary">🇺🇸 Switch to English</a>
                            <a href="?lang=ar" class="btn btn-outline-primary">🇸🇦 Switch to Arabic</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
