<?php require_once 'includes/config.php'; ?>
<?php require_once 'includes/header.php'; ?>

<?php
if (!isset($_GET['id'])) {
    header("Location: listing.php");
    exit;
}

$id = $_GET['id'];
$stmt = $pdo->prepare("SELECT * FROM phones WHERE id = ?");
$stmt->execute([$id]);
$phone = $stmt->fetch();

if (!$phone) {
    echo "<div class='alert alert-danger'>Record not found</div>";
    exit;
}
?>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2><?= htmlspecialchars($phone['brand']) ?> <?= htmlspecialchars($phone['model']) ?></h2>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Date Lost:</strong> <?= date('F j, Y', strtotime($phone['lost_date'])) ?></p>
                <p><strong>Location:</strong> <?= htmlspecialchars($phone['location']) ?></p>
                <?php if ($phone['imei']): ?>
                <p><strong>IMEI:</strong> <?= htmlspecialchars($phone['imei']) ?></p>
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <p><strong>Contact:</strong> <?= htmlspecialchars($phone['contact']) ?></p>
                <p><strong>Status:</strong> 
                    <span class="badge bg-<?= $phone['status'] == 'lost' ? 'danger' : 'success' ?>">
                        <?= ucfirst($phone['status']) ?>
                    </span>
                </p>
            </div>
        </div>
        
        <?php if (!empty($phone['description'])): ?>
        <div class="mt-4">
            <h4>Additional Details</h4>
            <p><?= nl2br(htmlspecialchars($phone['description'])) ?></p>
        </div>
        <?php endif; ?>
    </div>
</div>

<a href="listing.php" class="btn btn-secondary">Back to Listings</a>

<?php require_once 'includes/footer.php'; ?>