# PhoneFinder - Lost Phone Recovery System

A comprehensive web application for reporting and finding lost mobile devices, built with PHP, MySQL, and Bootstrap 5.

## Features

### 🔐 User Authentication
- **Secure Registration & Login**: Password hashing, CSRF protection, session management
- **User Profiles**: View account information and statistics
- **Remember Me**: Optional extended login sessions

### 📱 Device Management
- **Report Lost Phones**: Detailed forms with validation
- **Browse Listings**: Search and filter lost devices
- **My Reports**: Manage your own device reports
- **Mark as Found**: Update device status when recovered

### 🎨 Modern UI/UX
- **Responsive Design**: Mobile-first Bootstrap 5 interface
- **Accessibility**: ARIA labels, semantic HTML, keyboard navigation
- **Modern Animations**: Smooth hover effects and transitions
- **Dark/Light Theme Ready**: CSS custom properties for easy theming

### 🔒 Security Features
- **CSRF Protection**: All forms protected against cross-site request forgery
- **Input Validation**: Server-side validation and sanitization
- **SQL Injection Prevention**: Prepared statements throughout
- **Session Security**: Secure session handling and regeneration

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- XAMPP/WAMP/LAMP stack (recommended for development)

### Setup Instructions

1. **Clone or Download**
   ```bash
   git clone <repository-url>
   # or download and extract the ZIP file
   ```

2. **Database Setup**
   ```sql
   -- Create database
   CREATE DATABASE lost_phones CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   
   -- Create phones table (existing)
   CREATE TABLE phones (
       id INT AUTO_INCREMENT PRIMARY KEY,
       brand VARCHAR(100) NOT NULL,
       model VARCHAR(100) NOT NULL,
       imei VARCHAR(15) NULL,
       lost_date DATE NOT NULL,
       location VARCHAR(255) NOT NULL,
       contact VARCHAR(255) NOT NULL,
       description TEXT,
       status ENUM('lost', 'found') DEFAULT 'lost',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

3. **Run the User Table Creation Script**
   ```bash
   mysql -u root -p lost_phones < database/create_users_table.sql
   ```

4. **Configure Database Connection**
   Edit `includes/config.php`:
   ```php
   $host = 'localhost';
   $db   = 'lost_phones';
   $user = 'your_username';
   $pass = 'your_password';
   ```

5. **Set Permissions**
   Ensure the web server has read access to all files and write access to session storage.

6. **Access the Application**
   Navigate to `http://localhost/your-project-folder/` in your browser.

## File Structure

```
├── includes/
│   ├── config.php          # Database configuration
│   ├── auth.php           # Authentication functions
│   ├── header.php         # Common header with navigation
│   └── footer.php         # Common footer
├── assets/
│   └── css/
│       └── style.css      # Custom styles
├── database/
│   └── create_users_table.sql  # Database setup script
├── index.php              # Homepage
├── register.php           # User registration
├── login.php             # User login
├── logout.php            # User logout
├── profile.php           # User profile page
├── report.php            # Report lost device
├── listing.php           # Browse all listings
├── detail.php            # Device detail view
├── my-reports.php        # User's own reports
├── mark-found.php        # Mark device as found
└── README.md             # This file
```

## Usage

### For Users
1. **Register**: Create an account with username, email, and password
2. **Login**: Access your account securely
3. **Report Lost Device**: Fill out detailed form with device information
4. **Browse Listings**: Search for your lost device or help others
5. **Manage Reports**: View and update your own device reports
6. **Mark as Found**: Update status when device is recovered

### For Administrators
- User management through database
- Monitor reports and statistics
- Moderate content if needed

## Security Considerations

### Implemented Security Measures
- ✅ Password hashing with PHP's `password_hash()`
- ✅ CSRF token protection on all forms
- ✅ SQL injection prevention with prepared statements
- ✅ XSS prevention with `htmlspecialchars()`
- ✅ Session security with regeneration
- ✅ Input validation and sanitization
- ✅ Error logging without exposing sensitive data

### Additional Recommendations for Production
- [ ] HTTPS enforcement
- [ ] Rate limiting for login attempts
- [ ] Email verification for registration
- [ ] Password reset functionality
- [ ] Admin panel for moderation
- [ ] Backup and recovery procedures
- [ ] Regular security updates

## Customization

### Styling
- Edit `assets/css/style.css` for custom styles
- Bootstrap 5 classes can be modified
- CSS custom properties for easy theming

### Functionality
- Add new fields to device reports
- Implement email notifications
- Add image upload for devices
- Create admin dashboard
- Add geolocation features

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License
This project is open source and available under the MIT License.

## Support
For issues and questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

---

**PhoneFinder** - Helping reunite lost devices with their owners through community support.
